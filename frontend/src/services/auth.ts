import * as AppleAuthentication from 'expo-apple-authentication';
import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';
import { Platform } from 'react-native';
import { GoogleSignin } from '@react-native-google-signin/google-signin';

import { apiService } from './api';
import { OAUTH_CONFIG, FEATURES } from '@/constants';
import type {
  AppleAuthRequest,
  AuthResponse,
  GoogleAuthRequest,
  MicrosoftAuthRequest,
} from '@/types';

// Configure WebBrowser for auth sessions
WebBrowser.maybeCompleteAuthSession();

class AuthService {
  constructor() {
    this.initializeGoogleSignIn();
  }

  private initializeGoogleSignIn() {
    if (FEATURES.GOOGLE_SIGNIN) {
      GoogleSignin.configure({
        iosClientId: OAUTH_CONFIG.GOOGLE.CLIENT_ID_IOS,
        androidClientId: OAUTH_CONFIG.GOOGLE.CLIENT_ID_ANDROID,
        webClientId: OAUTH_CONFIG.GOOGLE.CLIENT_ID_WEB,
        offlineAccess: true,
      });
    }
  }

  // Apple Sign-In
  async signInWithApple(): Promise<AuthResponse> {
    if (!FEATURES.APPLE_SIGNIN || Platform.OS !== 'ios') {
      throw new Error('Apple Sign-In is not available on this platform');
    }

    try {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      const authRequest: AppleAuthRequest = {
        identity_token: credential.identityToken!,
        authorization_code: credential.authorizationCode!,
        user_info: credential.fullName ? {
          name: {
            firstName: credential.fullName.givenName || undefined,
            lastName: credential.fullName.familyName || undefined,
          },
          email: credential.email || undefined,
        } : undefined,
      };

      const response = await apiService.post<AuthResponse>('/auth/oauth/apple', authRequest);
      return response.data!;
    } catch (error: any) {
      if (error.code === 'ERR_CANCELED') {
        throw new Error('Apple Sign-In was canceled');
      }
      throw new Error(`Apple Sign-In failed: ${error.message}`);
    }
  }

  // Google Sign-In
  async signInWithGoogle(): Promise<AuthResponse> {
    if (!FEATURES.GOOGLE_SIGNIN) {
      throw new Error('Google Sign-In is not enabled');
    }

    try {
      await GoogleSignin.hasPlayServices();
      const userInfo = await GoogleSignin.signIn();
      
      const authRequest: GoogleAuthRequest = {
        id_token: userInfo.idToken!,
        access_token: userInfo.serverAuthCode || undefined,
      };

      const response = await apiService.post<AuthResponse>('/auth/oauth/google', authRequest);
      return response.data!;
    } catch (error: any) {
      if (error.code === 'SIGN_IN_CANCELLED') {
        throw new Error('Google Sign-In was canceled');
      }
      throw new Error(`Google Sign-In failed: ${error.message}`);
    }
  }

  // Microsoft Sign-In
  async signInWithMicrosoft(): Promise<AuthResponse> {
    if (!FEATURES.MICROSOFT_SIGNIN) {
      throw new Error('Microsoft Sign-In is not enabled');
    }

    try {
      const discovery = {
        authorizationEndpoint: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
        tokenEndpoint: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
      };

      const request = new AuthSession.AuthRequest({
        clientId: OAUTH_CONFIG.MICROSOFT.CLIENT_ID,
        scopes: ['openid', 'profile', 'email'],
        redirectUri: OAUTH_CONFIG.MICROSOFT.REDIRECT_URI,
        responseType: AuthSession.ResponseType.Code,
        additionalParameters: {},
        extraParams: {
          response_mode: 'query',
        },
      });

      const result = await request.promptAsync(discovery);

      if (result.type !== 'success') {
        throw new Error('Microsoft Sign-In was canceled or failed');
      }

      // Exchange code for tokens
      const tokenResponse = await AuthSession.exchangeCodeAsync(
        {
          clientId: OAUTH_CONFIG.MICROSOFT.CLIENT_ID,
          code: result.params.code,
          redirectUri: OAUTH_CONFIG.MICROSOFT.REDIRECT_URI,
          extraParams: {},
        },
        discovery
      );

      const authRequest: MicrosoftAuthRequest = {
        access_token: tokenResponse.accessToken,
        id_token: tokenResponse.idToken || undefined,
      };

      const response = await apiService.post<AuthResponse>('/auth/oauth/microsoft', authRequest);
      return response.data!;
    } catch (error: any) {
      throw new Error(`Microsoft Sign-In failed: ${error.message}`);
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      // Call backend logout endpoint
      await apiService.post('/auth/logout');
    } catch (error) {
      // Continue with local logout even if backend call fails
      console.warn('Backend logout failed:', error);
    }

    // Sign out from OAuth providers
    try {
      if (FEATURES.GOOGLE_SIGNIN) {
        await GoogleSignin.signOut();
      }
    } catch (error) {
      console.warn('Google sign out failed:', error);
    }

    // Note: Apple doesn't provide a sign out method
    // Microsoft sign out would require additional implementation
  }

  // Check if Apple Sign-In is available
  async isAppleSignInAvailable(): Promise<boolean> {
    if (!FEATURES.APPLE_SIGNIN || Platform.OS !== 'ios') {
      return false;
    }

    try {
      return await AppleAuthentication.isAvailableAsync();
    } catch {
      return false;
    }
  }

  // Check if Google Sign-In is available
  async isGoogleSignInAvailable(): Promise<boolean> {
    if (!FEATURES.GOOGLE_SIGNIN) {
      return false;
    }

    try {
      return await GoogleSignin.hasPlayServices();
    } catch {
      return false;
    }
  }

  // Check if Microsoft Sign-In is available
  isMicrosoftSignInAvailable(): boolean {
    return FEATURES.MICROSOFT_SIGNIN && !!OAUTH_CONFIG.MICROSOFT.CLIENT_ID;
  }
}

// Export singleton instance
export const authService = new AuthService();
