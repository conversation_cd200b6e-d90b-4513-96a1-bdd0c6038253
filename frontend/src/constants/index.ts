import Constants from 'expo-constants';

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.API_BASE_URL || 'http://localhost:8000',
  TIMEOUT: parseInt(process.env.API_TIMEOUT || '30000'),
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// OAuth Configuration
export const OAUTH_CONFIG = {
  GOOGLE: {
    CLIENT_ID_IOS: process.env.GOOGLE_CLIENT_ID_IOS || '',
    CLIENT_ID_ANDROID: process.env.GOOGLE_CLIENT_ID_ANDROID || '',
    CLIENT_ID_WEB: process.env.GOOGLE_CLIENT_ID_WEB || '',
  },
  APPLE: {
    CLIENT_ID: process.env.APPLE_CLIENT_ID || 'com.wheresz.mobile',
  },
  MICROSOFT: {
    CLIENT_ID: process.env.MICROSOFT_CLIENT_ID || '',
    REDIRECT_URI: process.env.MICROSOFT_REDIRECT_URI || 'wheresz://auth',
  },
};

// App Configuration
export const APP_CONFIG = {
  NAME: process.env.APP_NAME || 'WhereZ',
  VERSION: process.env.APP_VERSION || '1.0.0',
  DEBUG: process.env.DEBUG === 'true',
};

// Feature Flags
export const FEATURES = {
  APPLE_SIGNIN: process.env.ENABLE_APPLE_SIGNIN === 'true',
  GOOGLE_SIGNIN: process.env.ENABLE_GOOGLE_SIGNIN === 'true',
  MICROSOFT_SIGNIN: process.env.ENABLE_MICROSOFT_SIGNIN === 'true',
  AUDIO_QUESTIONS: process.env.ENABLE_AUDIO_QUESTIONS === 'true',
  IMAGE_CONTEXT: process.env.ENABLE_IMAGE_CONTEXT === 'true',
};

// Upload Configuration
export const UPLOAD_CONFIG = {
  MAX_IMAGE_SIZE: parseInt(process.env.MAX_IMAGE_SIZE || '10485760'), // 10MB
  MAX_AUDIO_DURATION: parseInt(process.env.MAX_AUDIO_DURATION || '300'), // 5 minutes
  SUPPORTED_IMAGE_FORMATS: (process.env.SUPPORTED_IMAGE_FORMATS || 'jpg,jpeg,png,gif,webp').split(','),
  SUPPORTED_AUDIO_FORMATS: (process.env.SUPPORTED_AUDIO_FORMATS || 'mp3,wav,m4a,aac').split(','),
};

// Cache Configuration
export const CACHE_CONFIG = {
  IMAGES: process.env.CACHE_IMAGES === 'true',
  DURATION: parseInt(process.env.CACHE_DURATION || '86400000'), // 24 hours
};

// Storage Keys
export const STORAGE_KEYS = {
  ACCESS_TOKEN: '@wheresz/access_token',
  REFRESH_TOKEN: '@wheresz/refresh_token',
  USER_DATA: '@wheresz/user_data',
  SETTINGS: '@wheresz/settings',
} as const;

// Colors
export const COLORS = {
  primary: '#007AFF',
  secondary: '#5856D6',
  success: '#34C759',
  warning: '#FF9500',
  error: '#FF3B30',
  info: '#5AC8FA',
  
  // Grays
  black: '#000000',
  darkGray: '#1C1C1E',
  gray: '#8E8E93',
  lightGray: '#C7C7CC',
  extraLightGray: '#F2F2F7',
  white: '#FFFFFF',
  
  // Background
  background: '#FFFFFF',
  surface: '#F2F2F7',
  card: '#FFFFFF',
  
  // Text
  text: '#000000',
  textSecondary: '#8E8E93',
  textDisabled: '#C7C7CC',
  
  // Border
  border: '#C7C7CC',
  separator: '#E5E5EA',
};

// Typography
export const TYPOGRAPHY = {
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  weights: {
    light: '300' as const,
    regular: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  lineHeights: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
  },
};

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

// Border Radius
export const BORDER_RADIUS = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  round: 9999,
};

// Shadows
export const SHADOWS = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
};

// Animation Durations
export const ANIMATION = {
  fast: 150,
  normal: 250,
  slow: 350,
};

// Screen Dimensions
export const SCREEN = {
  width: Constants.systemFonts ? 375 : 375, // Default fallback
  height: Constants.systemFonts ? 812 : 812, // Default fallback
};

// Question Status Colors
export const QUESTION_STATUS_COLORS = {
  pending: COLORS.warning,
  processing: COLORS.info,
  completed: COLORS.success,
  failed: COLORS.error,
};

// OAuth Provider Colors
export const OAUTH_COLORS = {
  apple: '#000000',
  google: '#4285F4',
  microsoft: '#00A4EF',
};
