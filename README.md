# WhereZ MVP Mobile Application

A complete mobile application MVP with React Native frontend and FastAPI backend, featuring OAuth authentication, image upload, and Q&A functionality.

## Project Structure

```
wheresz/
├── README.md                 # Main project documentation
├── docs/                     # API documentation and specifications
│   └── api-spec.yaml        # OpenAPI/Swagger specification
├── backend/                  # Python FastAPI backend
│   ├── app/                 # Main application code
│   ├── tests/               # Backend tests
│   ├── requirements.txt     # Python dependencies
│   ├── .env.example        # Environment variables example
│   └── README.md           # Backend setup instructions
├── frontend/                # React Native/Expo frontend
│   ├── src/                # Source code
│   ├── assets/             # Images, fonts, etc.
│   ├── __tests__/          # Frontend tests
│   ├── package.json        # Node.js dependencies
│   ├── .env.example       # Environment variables example
│   └── README.md          # Frontend setup instructions
└── .gitignore             # Git ignore file
```

## Features

### Authentication System
- OAuth2 integration for Apple Sign-In, Google Sign-In, and Microsoft Sign-In
- JWT token-based session management
- User profile management

### Image Upload Functionality
- Upload images from camera or photo library
- Image compression and validation
- Image storage and display

### Question & Answer System
- Text and audio input for questions
- Text and image-based answers
- Question/answer history

## Quick Start

### Prerequisites
- Node.js 18+ and npm/yarn
- Python 3.9+
- Expo CLI
- PostgreSQL (for production)

### Backend Setup
```bash
cd backend
pip install -r requirements.txt
cp .env.example .env
# Configure environment variables
python -m uvicorn app.main:app --reload
```

### Frontend Setup
```bash
cd frontend
npm install
cp .env.example .env
# Configure environment variables
npx expo start
```

## Documentation

- [API Documentation](docs/api-spec.yaml) - Complete OpenAPI specification
- [Backend README](backend/README.md) - Backend setup and deployment
- [Frontend README](frontend/README.md) - Frontend setup and development

## Technology Stack

### Backend
- **Framework**: FastAPI
- **Database**: SQLAlchemy ORM with PostgreSQL
- **Authentication**: OAuth2 + JWT
- **Language**: Python 3.9+ with TypeScript-style type hints

### Frontend
- **Framework**: React Native with Expo
- **Language**: TypeScript
- **Navigation**: React Navigation
- **State Management**: React Context + Hooks
- **HTTP Client**: Axios

## Development

### Environment Variables

Both frontend and backend require environment configuration. Copy the `.env.example` files and configure:

- OAuth provider credentials (Apple, Google, Microsoft)
- Database connection strings
- JWT secrets
- Image storage configuration

### Testing

```bash
# Backend tests
cd backend && python -m pytest

# Frontend tests
cd frontend && npm test
```

## Deployment

### Backend
- Deploy to cloud platforms (AWS, GCP, Azure)
- Configure PostgreSQL database
- Set up environment variables
- Enable HTTPS

### Frontend
- Build with Expo EAS Build
- Deploy to App Store and Google Play Store
- Configure deep linking and push notifications

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Submit a pull request

## License

MIT License - see LICENSE file for details
