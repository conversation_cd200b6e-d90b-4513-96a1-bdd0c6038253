# This is a sample Python script.

# Press <no shortcut> to execute it or replace it with your code.
# Press Double ⇧ to search everywhere for classes, files, tool windows, actions, and settings.


def print_hi(name):
    # Use a breakpoint in the code line below to debug your script.
    print(f'Hi, {name}')  # Press ⌘F8 to toggle the breakpoint.


# Press the green button in the gutter to run the script.
if __name__ == '__main__':
    print_hi('PyCharm')

# See PyCharm help at https://www.jetbrains.com/help/pycharm/


I want to create a mobile app for ios and android using Expo + React Native and python fastAPI+SQLAlchemy backend, as a MVP it should handle auth and login with apple/google/microsoft account and able to upload images, ask questions(text or audio input) and display answers(text and images). write the API doc as a file and write the front end and backend separately